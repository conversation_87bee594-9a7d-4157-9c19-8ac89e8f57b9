/**
 * Shared auction data reader that dynamically extracts auction information
 * from the frontend data.ts file to avoid hardcoding and ensure consistency
 */

const fs = require('fs');
const path = require('path');

// Cache for parsed auction data to avoid re-reading file on every request
let auctionDataCache = null;
let lastModified = null;

/**
 * Reads and parses the frontend data.ts file to extract auction information
 */
function loadAuctionDataFromFile() {
  try {
    const dataFilePath = path.join(__dirname, '../src/lib/data.ts');
    
    // Check if file exists
    if (!fs.existsSync(dataFilePath)) {
      console.warn('Frontend data.ts file not found at:', dataFilePath);
      return {};
    }

    // Check file modification time for caching
    const stats = fs.statSync(dataFilePath);
    if (auctionDataCache && lastModified && stats.mtime <= lastModified) {
      return auctionDataCache; // Return cached data if file hasn't changed
    }

    // Read the file content
    const fileContent = fs.readFileSync(dataFilePath, 'utf8');
    
    // Extract auction data using regex patterns
    const auctionData = {};
    
    // Pattern to match auction objects in the liveReels array
    // The structure is: { id: "...", ..., product: { ..., startingPrice: X, currentBid: Y, ... }, ..., bidCount: Z, ... }
    const auctionPattern = /{\s*id:\s*["']([^"']+)["'][\s\S]*?product:\s*{[\s\S]*?startingPrice:\s*(\d+)[\s\S]*?currentBid:\s*(\d+)[\s\S]*?}[\s\S]*?bidCount:\s*(\d+)[\s\S]*?}/g;
    
    let match;
    while ((match = auctionPattern.exec(fileContent)) !== null) {
      const [, id, startingPrice, currentBid, bidCount] = match;
      auctionData[id] = {
        startingPrice: parseInt(startingPrice, 10),
        currentBid: parseInt(currentBid, 10),
        bidCount: parseInt(bidCount, 10)
      };
    }

    // Update cache
    auctionDataCache = auctionData;
    lastModified = stats.mtime;
    
    console.log(`📊 Loaded auction data for ${Object.keys(auctionData).length} auctions from data.ts`);
    return auctionData;
    
  } catch (error) {
    console.error('Error loading auction data from data.ts:', error);
    return {};
  }
}

/**
 * Get auction data for a specific auction ID
 */
function getAuctionData(auctionId) {
  const allData = loadAuctionDataFromFile();
  return allData[auctionId] || null;
}

/**
 * Check if auction data exists for a specific auction ID
 */
function hasAuctionData(auctionId) {
  const allData = loadAuctionDataFromFile();
  return auctionId in allData;
}

/**
 * Get all auction data
 */
function getAllAuctionData() {
  return loadAuctionDataFromFile();
}

/**
 * Clear the cache (useful for development/testing)
 */
function clearCache() {
  auctionDataCache = null;
  lastModified = null;
}

module.exports = {
  getAuctionData,
  hasAuctionData,
  getAllAuctionData,
  clearCache
};
