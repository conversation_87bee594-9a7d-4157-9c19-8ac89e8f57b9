// Shared auction data for both frontend and backend
// This file provides a single source of truth for auction end times

// Auction end times configuration (in seconds from now)
const auctionEndTimes = {
  'fe0': 30,   // 30 seconds
  'fe1': 300,   // 1 minute
  'fe2': 330,   // 1.5 minutes
  'fe3': 360,  // 2 minutes
  'fe4': 390,  // 2.5 minutes
  'fe5': 420,  // 3 minutes
  'fe6': 450,  // 3.5 minutes
  'fe7': 480,  // 4 minutes
  'fe8': 510,   // 1.5 minutes
  'fe9': 540,   // 1 minute
  'fe10': 570,  // 1 minute
  'fe11': 600,  // 1 minute
  'fe12': 380,  // 1 minute
  'fe13': 400,  // 1 minute
  'fe14': 420,  // 1.2 minutes
  'fe15': 450,  // 1.2 minutes
  'pokemon-cards': 480,     // 1.2 minutes
  'first-class-upgrade': 500,  // 1.5 minutes
  'beats-earphones': 520,     // 1.8 minutes
  'bulgari-wonders': 570,      // 1 minute
};

// Default auction configuration
const defaultAuctionConfig = {
  startingBid: 100,
  bidIncrement: 5,
  defaultDuration: 60, // 1 minute default
};

// Helper function to get auction end time
function getAuctionEndTime(auctionId, baseTime = Date.now()) {
  const duration = auctionEndTimes[auctionId] || defaultAuctionConfig.defaultDuration;
  return baseTime + (duration * 1000);
}

// Helper function to get all auction IDs
function getAllAuctionIds() {
  return Object.keys(auctionEndTimes);
}

// Helper function to check if auction ID exists
function isValidAuctionId(auctionId) {
  return auctionEndTimes.hasOwnProperty(auctionId);
}

module.exports = {
  auctionEndTimes,
  defaultAuctionConfig,
  getAuctionEndTime,
  getAllAuctionIds,
  isValidAuctionId
};
