// Shared auction data for both frontend and backend
// This file provides a single source of truth for auction end times

// Auction end times configuration (in seconds from now)
const auctionEndTimes = {
  'fe0': 100,   // 30 seconds
  'fe1': 130,   // 1 minute
  'fe2': 160,   // 1.5 minutes
  'fe3': 180,  // 2 minutes
  'fe4': 200,  // 2.5 minutes
  'fe5': 210,  // 3 minutes
  'fe6': 230,  // 3.5 minutes
  'fe7': 250,  // 4 minutes
  'fe8': 280,   // 1.5 minutes
  'fe9': 300,   // 1 minute
  'fe10': 320,  // 1 minute
  'fe11': 350,  // 1 minute
  'fe12': 380,  // 1 minute
  'fe13': 400,  // 1 minute
  'fe14': 420,  // 1.2 minutes
  'fe15': 450,  // 1.2 minutes
  'pokemon-cards': 480,     // 1.2 minutes
  'first-class-upgrade': 500,  // 1.5 minutes
  'beats-earphones': 520,     // 1.8 minutes
  'bulgari-wonders': 570,      // 1 minute
};

// Default auction configuration
const defaultAuctionConfig = {
  startingBid: 100,
  bidIncrement: 5,
  defaultDuration: 60, // 1 minute default
};

// Helper function to get auction end time
function getAuctionEndTime(auctionId, baseTime = Date.now()) {
  const duration = auctionEndTimes[auctionId] || defaultAuctionConfig.defaultDuration;
  return baseTime + (duration * 1000);
}

// Helper function to get all auction IDs
function getAllAuctionIds() {
  return Object.keys(auctionEndTimes);
}

// Helper function to check if auction ID exists
function isValidAuctionId(auctionId) {
  return auctionEndTimes.hasOwnProperty(auctionId);
}

module.exports = {
  auctionEndTimes,
  defaultAuctionConfig,
  getAuctionEndTime,
  getAllAuctionIds,
  isValidAuctionId
};
