import { io } from 'socket.io-client';

// Test script to verify that new users receive existing bids when joining an auction

async function testExistingBids() {
  console.log('🧪 Testing existing bids functionality...');
  
  // Create first client to place some bids
  const client1 = io('http://localhost:3001');
  
  await new Promise((resolve) => {
    client1.on('connect', () => {
      console.log('✅ Client 1 connected');
      resolve();
    });
  });
  
  // Subscribe to auction and place some bids
  const auctionId = 'bulgari-wonders';
  client1.emit('subscribe_auction', auctionId);
  
  // Wait a bit then place first bid
  setTimeout(() => {
    console.log('📤 Client 1 placing first bid...');
    client1.emit('place_bid', {
      auctionId: auctionId,
      username: 'TestUser1',
      price: 150
    });
  }, 1000);
  
  // Place second bid
  setTimeout(() => {
    console.log('📤 Client 1 placing second bid...');
    client1.emit('place_bid', {
      auctionId: auctionId,
      username: 'TestUser1',
      price: 200
    });
  }, 2000);
  
  // Create second client after bids are placed
  setTimeout(async () => {
    console.log('🔌 Creating second client to test existing bids...');
    const client2 = io('http://localhost:3001');
    
    let receivedExistingBids = false;
    
    client2.on('connect', () => {
      console.log('✅ Client 2 connected');
      
      // Listen for existing bids
      client2.on('existing_bids', (data) => {
        console.log('🎯 Client 2 received existing bids:', data);
        receivedExistingBids = true;
        
        if (data.bids && data.bids.length > 0) {
          console.log('✅ SUCCESS: New user received existing bids!');
          console.log(`   - Received ${data.bids.length} existing bids`);
          data.bids.forEach((bid, index) => {
            console.log(`   - Bid ${index + 1}: $${bid.amount} by ${bid.bidder.name}`);
          });
        } else {
          console.log('❌ FAILURE: No existing bids received');
        }
        
        // Cleanup
        setTimeout(() => {
          client1.disconnect();
          client2.disconnect();
          console.log('🧹 Test completed, clients disconnected');
          process.exit(0);
        }, 1000);
      });
      
      // Subscribe to auction (this should trigger existing_bids event)
      client2.emit('subscribe_auction', auctionId);
      
      // Timeout check
      setTimeout(() => {
        if (!receivedExistingBids) {
          console.log('❌ TIMEOUT: Did not receive existing_bids event within 3 seconds');
          client1.disconnect();
          client2.disconnect();
          process.exit(1);
        }
      }, 3000);
    });
  }, 3000);
}

testExistingBids().catch(console.error);
